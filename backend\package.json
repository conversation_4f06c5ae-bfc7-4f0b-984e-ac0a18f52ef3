{"name": "srsr-property-management-backend", "version": "1.0.0", "description": "SRSR Property Management Backend API with Express.js and PostgreSQL", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit"}, "dependencies": {"@prisma/client": "^5.7.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "prisma": "^5.7.0", "rate-limiter-flexible": "^2.4.2", "sharp": "^0.33.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/nodemailer": "^6.4.14", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.55.0", "nodemon": "^3.0.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["property-management", "express", "postgresql", "prisma", "typescript", "rbac", "api"], "author": "SRSR Property Management", "license": "MIT"}