{"name": "srsr-property-management-backend", "version": "1.0.0", "description": "SRSR Property Management Backend API with Next.js, Express, and PostgreSQL", "main": "dist/server.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "server:dev": "nodemon src/server.ts", "server:build": "tsc && node dist/server.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "zod": "^3.22.4", "prisma": "^5.7.0", "@prisma/client": "^5.7.0", "multer": "^1.4.5-lts.1", "sharp": "^0.33.0", "nodemailer": "^6.9.7", "rate-limiter-flexible": "^2.4.2", "swagger-ui-express": "^5.0.0", "swagger-jsdoc": "^6.2.8", "dotenv": "^16.3.1", "uuid": "^9.0.1", "date-fns": "^2.30.0", "lodash": "^4.17.21"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/swagger-ui-express": "^4.1.6", "@types/swagger-jsdoc": "^6.0.4", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "typescript": "^5.3.0", "nodemon": "^3.0.2", "tsx": "^4.6.0", "eslint": "^8.55.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["property-management", "nextjs", "express", "postgresql", "prisma", "typescript", "rbac", "real-time", "api"], "author": "SRSR Property Management", "license": "MIT"}