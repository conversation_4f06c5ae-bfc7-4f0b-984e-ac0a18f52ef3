import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';
import 'package:flutter/foundation.dart';
import '../constants/api_constants.dart';
import '../../data/models/api_response.dart';
import '../../data/models/user.dart';
import 'api_client.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final ApiClient _apiClient = ApiClient();
  User? _currentUser;

  User? get currentUser => _currentUser;
  bool get isAuthenticated => _currentUser != null && _apiClient.isAuthenticated;

  Future<void> initialize() async {
    await _loadUserData();
  }

  Future<void> _loadUserData() async {
    final prefs = await SharedPreferences.getInstance();
    final userDataJson = prefs.getString(ApiConstants.userDataKey);
    
    if (userDataJson != null) {
      try {
        final userData = jsonDecode(userDataJson);
        _currentUser = User.fromJson(userData);
      } catch (e) {
        // Invalid user data, clear it
        await _clearUserData();
      }
    }
  }

  Future<void> _saveUserData(User user) async {
    _currentUser = user;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(ApiConstants.userDataKey, jsonEncode(user.toJson()));
  }

  Future<void> _clearUserData() async {
    _currentUser = null;
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(ApiConstants.userDataKey);
  }

  Future<String> _getDeviceId() async {
    final prefs = await SharedPreferences.getInstance();
    String? deviceId = prefs.getString(ApiConstants.deviceIdKey);

    if (deviceId == null) {
      try {
        final deviceInfo = DeviceInfoPlugin();

        if (kIsWeb) {
          final webInfo = await deviceInfo.webBrowserInfo;
          deviceId = '${webInfo.browserName}_${webInfo.userAgent?.hashCode}';
        } else if (Platform.isAndroid) {
          final androidInfo = await deviceInfo.androidInfo;
          deviceId = androidInfo.id;
        } else if (Platform.isIOS) {
          final iosInfo = await deviceInfo.iosInfo;
          deviceId = iosInfo.identifierForVendor ?? 'ios_unknown';
        } else if (Platform.isWindows) {
          final windowsInfo = await deviceInfo.windowsInfo;
          deviceId = windowsInfo.computerName;
        } else if (Platform.isMacOS) {
          final macInfo = await deviceInfo.macOsInfo;
          deviceId = macInfo.systemGUID ?? 'macos_unknown';
        } else if (Platform.isLinux) {
          final linuxInfo = await deviceInfo.linuxInfo;
          deviceId = linuxInfo.machineId ?? 'linux_unknown';
        } else {
          deviceId = 'unknown_device_${DateTime.now().millisecondsSinceEpoch}';
        }
      } catch (e) {
        // Fallback if platform detection fails
        deviceId = 'fallback_device_${DateTime.now().millisecondsSinceEpoch}';
        if (kDebugMode) {
          print('Device ID generation failed: $e');
        }
      }

      await prefs.setString(ApiConstants.deviceIdKey, deviceId);
    }

    return deviceId;
  }

  Future<String> _getDeviceName() async {
    try {
      final deviceInfo = DeviceInfoPlugin();

      if (kIsWeb) {
        final webInfo = await deviceInfo.webBrowserInfo;
        return '${webInfo.browserName} on ${webInfo.platform}';
      } else if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        return '${androidInfo.brand} ${androidInfo.model}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        return '${iosInfo.name} (${iosInfo.model})';
      } else if (Platform.isWindows) {
        final windowsInfo = await deviceInfo.windowsInfo;
        return 'Windows PC (${windowsInfo.computerName})';
      } else if (Platform.isMacOS) {
        final macInfo = await deviceInfo.macOsInfo;
        return 'Mac (${macInfo.computerName})';
      } else if (Platform.isLinux) {
        final linuxInfo = await deviceInfo.linuxInfo;
        return 'Linux (${linuxInfo.name})';
      } else {
        return 'Unknown Device';
      }
    } catch (e) {
      if (kDebugMode) {
        print('Device name generation failed: $e');
      }
      return 'Unknown Device';
    }
  }

  Future<ApiResponse<LoginResponse>> login({
    required String email,
    required String password,
  }) async {
    try {
      // Try to get device info, but don't fail if it's not available
      String? deviceId;
      String? deviceName;

      try {
        deviceId = await _getDeviceId();
        deviceName = await _getDeviceName();
      } catch (e) {
        if (kDebugMode) {
          print('Failed to get device info: $e');
        }
        // Continue without device info
      }

      final requestData = <String, dynamic>{
        'email': email,
        'password': password,
      };

      // Only add device fields if they're available
      if (deviceId != null) requestData['deviceId'] = deviceId;
      if (deviceName != null) requestData['deviceName'] = deviceName;

      final response = await _apiClient.post<LoginResponse>(
        ApiConstants.login,
        data: requestData,
        fromJson: (json) => LoginResponse.fromJson(json),
      );

      if (response.isSuccess && response.data != null) {
        final loginData = response.data!;
        
        // Save tokens
        await _apiClient.setTokens(
          loginData.token.accessToken,
          loginData.token.refreshToken,
        );
        
        // Save user data
        await _saveUserData(loginData.user);
      }

      return response;
    } catch (e) {
      return ApiResponse<LoginResponse>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Login failed: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  Future<ApiResponse<void>> logout() async {
    try {
      // Call logout endpoint
      await _apiClient.post<void>(ApiConstants.logout);
      
      // Clear local data regardless of API response
      await _apiClient.clearTokens();
      await _clearUserData();
      
      return ApiResponse<void>(
        success: true,
        message: 'Logged out successfully',
        timestamp: DateTime.now().toIso8601String(),
      );
    } catch (e) {
      // Clear local data even if API call fails
      await _apiClient.clearTokens();
      await _clearUserData();
      
      return ApiResponse<void>(
        success: true,
        message: 'Logged out successfully',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  Future<ApiResponse<User>> getCurrentUser() async {
    try {
      final response = await _apiClient.get<User>(
        ApiConstants.me,
        fromJson: (json) => User.fromJson(json),
      );

      if (response.isSuccess && response.data != null) {
        await _saveUserData(response.data!);
      }

      return response;
    } catch (e) {
      return ApiResponse<User>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to get user data: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  Future<ApiResponse<User>> updateProfile({
    String? name,
    String? phone,
    String? avatar,
    String? timezone,
    String? language,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (name != null) data['name'] = name;
      if (phone != null) data['phone'] = phone;
      if (avatar != null) data['avatar'] = avatar;
      if (timezone != null) data['timezone'] = timezone;
      if (language != null) data['language'] = language;

      final response = await _apiClient.put<User>(
        ApiConstants.me,
        data: data,
        fromJson: (json) => User.fromJson(json),
      );

      if (response.isSuccess && response.data != null) {
        await _saveUserData(response.data!);
      }

      return response;
    } catch (e) {
      return ApiResponse<User>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to update profile: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  Future<ApiResponse<AuthToken>> refreshToken() async {
    try {
      if (_apiClient.refreshToken == null) {
        return ApiResponse<AuthToken>(
          success: false,
          error: ApiConstants.errorUnauthorized,
          message: 'No refresh token available',
          timestamp: DateTime.now().toIso8601String(),
        );
      }

      final response = await _apiClient.post<AuthToken>(
        ApiConstants.refresh,
        data: {
          'refreshToken': _apiClient.refreshToken,
        },
        fromJson: (json) => AuthToken.fromJson(json),
      );

      if (response.isSuccess && response.data != null) {
        final tokenData = response.data!;
        await _apiClient.setTokens(
          tokenData.accessToken,
          tokenData.refreshToken,
        );
      }

      return response;
    } catch (e) {
      return ApiResponse<AuthToken>(
        success: false,
        error: ApiConstants.errorServerError,
        message: 'Failed to refresh token: ${e.toString()}',
        timestamp: DateTime.now().toIso8601String(),
      );
    }
  }

  // Check if user has specific permission
  bool hasPermission(String permission) {
    final permissions = _currentUser?.permissions;
    if (permissions == null) return false;

    switch (permission) {
      case 'canViewDashboard':
        return permissions.canViewDashboard;
      case 'canManageProperties':
        return permissions.canManageProperties;
      case 'canManageOffice':
        return permissions.canManageOffice;
      case 'canManageSecurity':
        return permissions.canManageSecurity;
      case 'canManageMaintenance':
        return permissions.canManageMaintenance;
      case 'canManageUsers':
        return permissions.canManageUsers;
      case 'canViewReports':
        return permissions.canViewReports;
      case 'canExportData':
        return permissions.canExportData;
      default:
        return false;
    }
  }

  // Check if user can access specific screen
  bool canAccessScreen(String screen) {
    final user = _currentUser;
    if (user == null) return false;
    return user.permissions.allowedScreens.contains(screen);
  }

  // Check if user can perform specific action
  bool canPerformAction(String action) {
    final user = _currentUser;
    if (user == null) return false;
    return user.permissions.allowedActions.contains(action);
  }

  // Check if user has access to specific property
  bool hasPropertyAccess(String propertyId) {
    if (_currentUser?.role == 'SUPER_ADMIN') return true;
    return _currentUser?.assignedProperties.contains(propertyId) ?? false;
  }
}
