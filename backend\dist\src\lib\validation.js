"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileUploadSchema = exports.dashboardQuerySchema = exports.attendanceQuerySchema = exports.alertQuerySchema = exports.propertyQuerySchema = exports.paginationSchema = exports.updateAlertSchema = exports.createAlertSchema = exports.submitAttendanceSchema = exports.createEmployeeSchema = exports.createOfficeSchema = exports.updateSystemSchema = exports.updatePropertySchema = exports.createPropertySchema = exports.updateUserSchema = exports.createUserSchema = exports.refreshTokenSchema = exports.loginSchema = void 0;
exports.validateUUID = validateUUID;
exports.validateEmail = validateEmail;
exports.validatePhone = validatePhone;
exports.validateDate = validateDate;
exports.validateTime = validateTime;
const zod_1 = require("zod");
// Authentication Schemas
exports.loginSchema = zod_1.z.object({
    email: zod_1.z.string().email('Invalid email format'),
    password: zod_1.z.string().min(6, 'Password must be at least 6 characters'),
    deviceId: zod_1.z.string().optional(),
    deviceName: zod_1.z.string().optional(),
});
exports.refreshTokenSchema = zod_1.z.object({
    refreshToken: zod_1.z.string().min(1, 'Refresh token is required'),
});
// User Schemas
exports.createUserSchema = zod_1.z.object({
    name: zod_1.z.string().min(2, 'Name must be at least 2 characters'),
    email: zod_1.z.string().email('Invalid email format'),
    phone: zod_1.z.string().regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format'),
    role: zod_1.z.enum(['SUPER_ADMIN', 'PROPERTY_MANAGER', 'OFFICE_MANAGER', 'SECURITY_PERSONNEL', 'MAINTENANCE_STAFF', 'CONSTRUCTION_SUPERVISOR']),
    assignedProperties: zod_1.z.array(zod_1.z.string().uuid()).optional(),
    password: zod_1.z.string().min(6, 'Password must be at least 6 characters').optional(),
});
exports.updateUserSchema = zod_1.z.object({
    name: zod_1.z.string().min(2, 'Name must be at least 2 characters').optional(),
    phone: zod_1.z.string().regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format').optional(),
    avatar: zod_1.z.string().url('Invalid avatar URL').optional(),
    timezone: zod_1.z.string().optional(),
    language: zod_1.z.enum(['en', 'hi', 'te']).optional(),
});
// Property Schemas
exports.createPropertySchema = zod_1.z.object({
    name: zod_1.z.string().min(2, 'Property name must be at least 2 characters'),
    type: zod_1.z.enum(['RESIDENTIAL', 'OFFICE', 'CONSTRUCTION']),
    address: zod_1.z.string().min(10, 'Address must be at least 10 characters'),
    description: zod_1.z.string().min(10, 'Description must be at least 10 characters'),
    latitude: zod_1.z.number().min(-90).max(90).optional(),
    longitude: zod_1.z.number().min(-180).max(180).optional(),
    images: zod_1.z.array(zod_1.z.string().url()).optional(),
});
exports.updatePropertySchema = zod_1.z.object({
    name: zod_1.z.string().min(2, 'Property name must be at least 2 characters').optional(),
    address: zod_1.z.string().min(10, 'Address must be at least 10 characters').optional(),
    description: zod_1.z.string().min(10, 'Description must be at least 10 characters').optional(),
    latitude: zod_1.z.number().min(-90).max(90).optional(),
    longitude: zod_1.z.number().min(-180).max(180).optional(),
    images: zod_1.z.array(zod_1.z.string().url()).optional(),
    isActive: zod_1.z.boolean().optional(),
});
exports.updateSystemSchema = zod_1.z.object({
    status: zod_1.z.enum(['OPERATIONAL', 'WARNING', 'CRITICAL', 'OFFLINE']),
    description: zod_1.z.string().optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
    healthScore: zod_1.z.number().min(0).max(100).optional(),
});
// Office & Attendance Schemas
exports.createOfficeSchema = zod_1.z.object({
    name: zod_1.z.string().min(2, 'Office name must be at least 2 characters'),
    type: zod_1.z.enum(['OFFICE', 'CONSTRUCTION_SITE']),
    address: zod_1.z.string().min(10, 'Address must be at least 10 characters'),
    latitude: zod_1.z.number().min(-90).max(90).optional(),
    longitude: zod_1.z.number().min(-180).max(180).optional(),
    workingHours: zod_1.z.record(zod_1.z.any()).optional(),
});
exports.createEmployeeSchema = zod_1.z.object({
    officeId: zod_1.z.string().uuid('Invalid office ID'),
    name: zod_1.z.string().min(2, 'Employee name must be at least 2 characters'),
    email: zod_1.z.string().email('Invalid email format').optional(),
    phone: zod_1.z.string().regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format').optional(),
    employeeId: zod_1.z.string().min(1, 'Employee ID is required'),
    designation: zod_1.z.string().min(2, 'Designation must be at least 2 characters'),
    department: zod_1.z.string().optional(),
    joinDate: zod_1.z.string().datetime('Invalid join date format'),
});
exports.submitAttendanceSchema = zod_1.z.object({
    date: zod_1.z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format'),
    records: zod_1.z.array(zod_1.z.object({
        employeeId: zod_1.z.string().uuid().optional(),
        userId: zod_1.z.string().uuid().optional(),
        status: zod_1.z.enum(['PRESENT', 'ABSENT', 'LATE', 'HALF_DAY', 'LEAVE']),
        checkInTime: zod_1.z.string().regex(/^\d{2}:\d{2}$/, 'Check-in time must be in HH:MM format').optional(),
        checkOutTime: zod_1.z.string().regex(/^\d{2}:\d{2}$/, 'Check-out time must be in HH:MM format').optional(),
        hoursWorked: zod_1.z.number().min(0).max(24).optional(),
        overtime: zod_1.z.number().min(0).max(12).optional(),
        notes: zod_1.z.string().optional(),
    })).min(1, 'At least one attendance record is required'),
});
// Alert Schemas
exports.createAlertSchema = zod_1.z.object({
    propertyId: zod_1.z.string().uuid().optional(),
    title: zod_1.z.string().min(5, 'Alert title must be at least 5 characters'),
    message: zod_1.z.string().min(10, 'Alert message must be at least 10 characters'),
    severity: zod_1.z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']),
    category: zod_1.z.string().optional(),
    metadata: zod_1.z.record(zod_1.z.any()).optional(),
});
exports.updateAlertSchema = zod_1.z.object({
    status: zod_1.z.enum(['OPEN', 'ACKNOWLEDGED', 'RESOLVED']).optional(),
    resolvedAt: zod_1.z.string().datetime().optional(),
});
// Query Parameter Schemas
exports.paginationSchema = zod_1.z.object({
    page: zod_1.z.coerce.number().min(1).default(1),
    limit: zod_1.z.coerce.number().min(1).max(100).default(20),
});
exports.propertyQuerySchema = exports.paginationSchema.extend({
    type: zod_1.z.enum(['RESIDENTIAL', 'OFFICE', 'CONSTRUCTION']).optional(),
    status: zod_1.z.enum(['OPERATIONAL', 'WARNING', 'CRITICAL', 'OFFLINE']).optional(),
    search: zod_1.z.string().optional(),
});
exports.alertQuerySchema = exports.paginationSchema.extend({
    severity: zod_1.z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']).optional(),
    status: zod_1.z.enum(['OPEN', 'ACKNOWLEDGED', 'RESOLVED']).optional(),
    propertyId: zod_1.z.string().uuid().optional(),
});
exports.attendanceQuerySchema = zod_1.z.object({
    date: zod_1.z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in YYYY-MM-DD format').optional(),
    startDate: zod_1.z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Start date must be in YYYY-MM-DD format').optional(),
    endDate: zod_1.z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'End date must be in YYYY-MM-DD format').optional(),
});
exports.dashboardQuerySchema = zod_1.z.object({
    propertyIds: zod_1.z.string().optional(),
    timeRange: zod_1.z.enum(['24h', '7d', '30d', '90d']).default('24h'),
});
// File Upload Schema
exports.fileUploadSchema = zod_1.z.object({
    fieldname: zod_1.z.string(),
    originalname: zod_1.z.string(),
    encoding: zod_1.z.string(),
    mimetype: zod_1.z.string().refine((type) => ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'].includes(type), 'Invalid file type. Only JPEG, PNG, WebP, and PDF files are allowed.'),
    size: zod_1.z.number().max(10 * 1024 * 1024, 'File size must be less than 10MB'),
});
// Validation Helper Functions
function validateUUID(id) {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(id);
}
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}
function validatePhone(phone) {
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone);
}
function validateDate(date) {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(date))
        return false;
    const parsedDate = new Date(date);
    return parsedDate instanceof Date && !isNaN(parsedDate.getTime());
}
function validateTime(time) {
    const timeRegex = /^\d{2}:\d{2}$/;
    if (!timeRegex.test(time))
        return false;
    const [hours, minutes] = time.split(':').map(Number);
    return hours >= 0 && hours <= 23 && minutes >= 0 && minutes <= 59;
}
