import Link from 'next/link';

export default function HomePage() {
  return (
    <div style={{ 
      fontFamily: 'system-ui, -apple-system, sans-serif',
      maxWidth: '800px',
      margin: '0 auto',
      padding: '2rem',
      lineHeight: '1.6'
    }}>
      <header style={{ textAlign: 'center', marginBottom: '3rem' }}>
        <h1 style={{ color: '#2563eb', marginBottom: '0.5rem' }}>
          🏢 SRSR Property Management API
        </h1>
        <p style={{ color: '#6b7280', fontSize: '1.1rem' }}>
          Comprehensive API for property management with RBAC and real-time updates
        </p>
      </header>

      <main>
        <section style={{ marginBottom: '2rem' }}>
          <h2 style={{ color: '#374151', borderBottom: '2px solid #e5e7eb', paddingBottom: '0.5rem' }}>
            🚀 Quick Start
          </h2>
          <div style={{ 
            backgroundColor: '#f9fafb', 
            padding: '1.5rem', 
            borderRadius: '8px',
            border: '1px solid #e5e7eb',
            marginTop: '1rem'
          }}>
            <h3 style={{ margin: '0 0 1rem 0', color: '#1f2937' }}>API Base URL</h3>
            <code style={{ 
              backgroundColor: '#1f2937', 
              color: '#f9fafb', 
              padding: '0.5rem 1rem',
              borderRadius: '4px',
              display: 'block',
              fontFamily: 'monospace'
            }}>
              {process.env.NODE_ENV === 'production' 
                ? 'https://api.srsrproperty.com/v1' 
                : 'http://localhost:3000/api'
              }
            </code>
          </div>
        </section>

        <section style={{ marginBottom: '2rem' }}>
          <h2 style={{ color: '#374151', borderBottom: '2px solid #e5e7eb', paddingBottom: '0.5rem' }}>
            📚 Documentation & Tools
          </h2>
          <div style={{ display: 'grid', gap: '1rem', marginTop: '1rem' }}>
            <Link 
              href="/api-docs" 
              style={{ 
                display: 'block',
                padding: '1rem',
                backgroundColor: '#dbeafe',
                border: '1px solid #93c5fd',
                borderRadius: '8px',
                textDecoration: 'none',
                color: '#1e40af',
                transition: 'all 0.2s'
              }}
            >
              <strong>📖 API Documentation</strong>
              <br />
              <span style={{ color: '#3730a3' }}>Interactive Swagger UI with all endpoints</span>
            </Link>

            <Link 
              href="/health" 
              style={{ 
                display: 'block',
                padding: '1rem',
                backgroundColor: '#dcfce7',
                border: '1px solid #86efac',
                borderRadius: '8px',
                textDecoration: 'none',
                color: '#166534',
                transition: 'all 0.2s'
              }}
            >
              <strong>🏥 Health Check</strong>
              <br />
              <span style={{ color: '#15803d' }}>Basic API health status</span>
            </Link>

            <Link 
              href="/health/detailed" 
              style={{ 
                display: 'block',
                padding: '1rem',
                backgroundColor: '#fef3c7',
                border: '1px solid #fcd34d',
                borderRadius: '8px',
                textDecoration: 'none',
                color: '#92400e',
                transition: 'all 0.2s'
              }}
            >
              <strong>🔍 Detailed Health Check</strong>
              <br />
              <span style={{ color: '#b45309' }}>Comprehensive system health metrics</span>
            </Link>
          </div>
        </section>

        <section style={{ marginBottom: '2rem' }}>
          <h2 style={{ color: '#374151', borderBottom: '2px solid #e5e7eb', paddingBottom: '0.5rem' }}>
            🔐 Authentication
          </h2>
          <div style={{ 
            backgroundColor: '#fef2f2', 
            padding: '1.5rem', 
            borderRadius: '8px',
            border: '1px solid #fecaca',
            marginTop: '1rem'
          }}>
            <h3 style={{ margin: '0 0 1rem 0', color: '#991b1b' }}>Default Admin Credentials</h3>
            <p style={{ margin: '0.5rem 0', color: '#7f1d1d' }}>
              <strong>Email:</strong> <EMAIL><br />
              <strong>Password:</strong> admin123
            </p>
            <p style={{ margin: '1rem 0 0 0', fontSize: '0.9rem', color: '#991b1b' }}>
              ⚠️ Change these credentials in production!
            </p>
          </div>
        </section>

        <section style={{ marginBottom: '2rem' }}>
          <h2 style={{ color: '#374151', borderBottom: '2px solid #e5e7eb', paddingBottom: '0.5rem' }}>
            🌟 Features
          </h2>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
            gap: '1rem',
            marginTop: '1rem'
          }}>
            {[
              { icon: '🔐', title: 'RBAC Authentication', desc: '6 user roles with granular permissions' },
              { icon: '🏢', title: 'Property Management', desc: 'Multi-property support with system monitoring' },
              { icon: '👥', title: 'Office Management', desc: 'Attendance tracking and employee management' },
              { icon: '⚡', title: 'Real-time Updates', desc: 'WebSocket support for live data' },
              { icon: '📊', title: 'Dashboard Analytics', desc: 'Comprehensive reporting and statistics' },
              { icon: '🛡️', title: 'Security Features', desc: 'Rate limiting, validation, and monitoring' },
            ].map((feature, index) => (
              <div 
                key={index}
                style={{ 
                  padding: '1rem',
                  backgroundColor: '#f8fafc',
                  border: '1px solid #e2e8f0',
                  borderRadius: '8px'
                }}
              >
                <div style={{ fontSize: '1.5rem', marginBottom: '0.5rem' }}>{feature.icon}</div>
                <h3 style={{ margin: '0 0 0.5rem 0', color: '#1e293b' }}>{feature.title}</h3>
                <p style={{ margin: '0', color: '#64748b', fontSize: '0.9rem' }}>{feature.desc}</p>
              </div>
            ))}
          </div>
        </section>

        <section>
          <h2 style={{ color: '#374151', borderBottom: '2px solid #e5e7eb', paddingBottom: '0.5rem' }}>
            🔗 Quick API Examples
          </h2>
          <div style={{ marginTop: '1rem' }}>
            <h3 style={{ color: '#1f2937', marginBottom: '0.5rem' }}>Login</h3>
            <pre style={{ 
              backgroundColor: '#1f2937', 
              color: '#f9fafb', 
              padding: '1rem',
              borderRadius: '4px',
              overflow: 'auto',
              fontSize: '0.9rem'
            }}>
{`POST /v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "admin123"
}`}
            </pre>

            <h3 style={{ color: '#1f2937', marginBottom: '0.5rem', marginTop: '1.5rem' }}>Get Properties</h3>
            <pre style={{ 
              backgroundColor: '#1f2937', 
              color: '#f9fafb', 
              padding: '1rem',
              borderRadius: '4px',
              overflow: 'auto',
              fontSize: '0.9rem'
            }}>
{`GET /v1/properties?page=1&limit=20
Authorization: Bearer <your-jwt-token>`}
            </pre>
          </div>
        </section>
      </main>

      <footer style={{ 
        textAlign: 'center', 
        marginTop: '3rem', 
        paddingTop: '2rem',
        borderTop: '1px solid #e5e7eb',
        color: '#6b7280'
      }}>
        <p>
          Built with ❤️ using Next.js, Express.js, and PostgreSQL
        </p>
        <p style={{ fontSize: '0.9rem' }}>
          © 2024 SRSR Property Management. All rights reserved.
        </p>
      </footer>
    </div>
  );
}
