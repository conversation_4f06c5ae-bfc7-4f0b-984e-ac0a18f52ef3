#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1858752 bytes for Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   The process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:189), pid=15408, tid=46696
#
# JRE version: OpenJDK Runtime Environment (17.0+35) (build 17+35-2724)
# Java VM: OpenJDK 64-Bit Server VM (17+35-2724, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -Xmx8G -Dfile.encoding=UTF-8 -Duser.country=IN -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12

Host: 11th Gen Intel(R) Core(TM) i7-1165G7 @ 2.80GHz, 8 cores, 15G,  Windows 10 , 64 bit Build 26100 (10.0.26100.3912)
Time: Wed Jun  4 11:51:58 2025 India Standard Time elapsed time: 20.616261 seconds (0d 0h 0m 20s)

---------------  T H R E A D  ---------------

Current thread (0x000002abd5fe1140):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=46696, stack(0x000000e74ee00000,0x000000e74ef00000)]


Current CompileTask:
C2:  20616 6275       4       java.io.ObjectOutputStream::writeClassDesc (60 bytes)

Stack: [0x000000e74ee00000,0x000000e74ef00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x66f8ea]
V  [jvm.dll+0x7ce224]
V  [jvm.dll+0x7cf9ce]
V  [jvm.dll+0x7d0033]
V  [jvm.dll+0x242bb5]
V  [jvm.dll+0xaa05b]
V  [jvm.dll+0xaa5ec]
V  [jvm.dll+0x35f9f7]
V  [jvm.dll+0x329f61]
V  [jvm.dll+0x32940a]
V  [jvm.dll+0x215ca1]
V  [jvm.dll+0x2150c1]
V  [jvm.dll+0x1a168d]
V  [jvm.dll+0x2248f8]
V  [jvm.dll+0x222a5c]
V  [jvm.dll+0x78422b]
V  [jvm.dll+0x77e7ea]
V  [jvm.dll+0x66e715]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x9c5dc]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002abdd141e90, length=64, elements={
0x000002ab9b0c33c0, 0x000002abd5fbf430, 0x000002abd5fbfef0, 0x000002abd5fd25c0,
0x000002abd5fd4e90, 0x000002abd5fd7760, 0x000002abd5fd9030, 0x000002abd5fe1140,
0x000002abd9420090, 0x000002abd9429160, 0x000002abd5fae800, 0x000002abd95c4420,
0x000002abdb4eba40, 0x000002abdb417bd0, 0x000002abdb6ba9d0, 0x000002abdb398f30,
0x000002abdb53e2b0, 0x000002abdb64abd0, 0x000002abdb53dbf0, 0x000002abdb671f00,
0x000002abdb6723d0, 0x000002abdb671560, 0x000002abdb673710, 0x000002abdb671a30,
0x000002abdb6740b0, 0x000002abdb673be0, 0x000002abdb674580, 0x000002abdb674a50,
0x000002abdb6728a0, 0x000002abdb673240, 0x000002abdb671090, 0x000002abdd5f5be0,
0x000002abdd5f73f0, 0x000002abdd5f4d70, 0x000002abdd5f6580, 0x000002abdd5f43d0,
0x000002abdd5f60b0, 0x000002abdd5f7d90, 0x000002abdd5f6f20, 0x000002abdd5f8260,
0x000002abdd5fb280, 0x000002abdd5f8730, 0x000002abddaa6840, 0x000002abdd5f8c00,
0x000002abdd5f90d0, 0x000002abdd5f95a0, 0x000002abdd5f9a70, 0x000002abdd5fa8e0,
0x000002abdd6b1780, 0x000002abdd6b0de0, 0x000002abdd6b12b0, 0x000002abdd6aff70,
0x000002abdd6b2ac0, 0x000002abdd6af5d0, 0x000002abdd6b25f0, 0x000002abdd6b1c50,
0x000002abdd6af100, 0x000002abdd6afaa0, 0x000002abdd6b2120, 0x000002abdd6b3460,
0x000002abdd6b2f90, 0x000002abdd6b0440, 0x000002abdd6b3930, 0x000002abdd6b3e00
}

Java Threads: ( => current thread )
  0x000002ab9b0c33c0 JavaThread "main" [_thread_blocked, id=60724, stack(0x000000e74e100000,0x000000e74e200000)]
  0x000002abd5fbf430 JavaThread "Reference Handler" daemon [_thread_blocked, id=59996, stack(0x000000e74e800000,0x000000e74e900000)]
  0x000002abd5fbfef0 JavaThread "Finalizer" daemon [_thread_blocked, id=54732, stack(0x000000e74e900000,0x000000e74ea00000)]
  0x000002abd5fd25c0 JavaThread "Signal Dispatcher" daemon [_thread_blocked, id=70192, stack(0x000000e74ea00000,0x000000e74eb00000)]
  0x000002abd5fd4e90 JavaThread "Attach Listener" daemon [_thread_blocked, id=68868, stack(0x000000e74eb00000,0x000000e74ec00000)]
  0x000002abd5fd7760 JavaThread "Service Thread" daemon [_thread_blocked, id=30856, stack(0x000000e74ec00000,0x000000e74ed00000)]
  0x000002abd5fd9030 JavaThread "Monitor Deflation Thread" daemon [_thread_blocked, id=13248, stack(0x000000e74ed00000,0x000000e74ee00000)]
=>0x000002abd5fe1140 JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=46696, stack(0x000000e74ee00000,0x000000e74ef00000)]
  0x000002abd9420090 JavaThread "C1 CompilerThread0" daemon [_thread_in_native, id=76136, stack(0x000000e74ef00000,0x000000e74f000000)]
  0x000002abd9429160 JavaThread "Sweeper thread" daemon [_thread_blocked, id=71240, stack(0x000000e74f000000,0x000000e74f100000)]
  0x000002abd5fae800 JavaThread "Common-Cleaner" daemon [_thread_blocked, id=67260, stack(0x000000e74f100000,0x000000e74f200000)]
  0x000002abd95c4420 JavaThread "Notification Thread" daemon [_thread_blocked, id=59508, stack(0x000000e74f200000,0x000000e74f300000)]
  0x000002abdb4eba40 JavaThread "Daemon health stats" [_thread_blocked, id=70632, stack(0x000000e74f500000,0x000000e74f600000)]
  0x000002abdb417bd0 JavaThread "Incoming local TCP Connector on port 60529" [_thread_in_native, id=51680, stack(0x000000e74f400000,0x000000e74f500000)]
  0x000002abdb6ba9d0 JavaThread "Daemon periodic checks" [_thread_blocked, id=76772, stack(0x000000e74fb00000,0x000000e74fc00000)]
  0x000002abdb398f30 JavaThread "Daemon" [_thread_blocked, id=21700, stack(0x000000e74fc00000,0x000000e74fd00000)]
  0x000002abdb53e2b0 JavaThread "Handler for socket connection from /127.0.0.1:60529 to /127.0.0.1:60531" [_thread_in_native, id=71332, stack(0x000000e74fd00000,0x000000e74fe00000)]
  0x000002abdb64abd0 JavaThread "Cancel handler" [_thread_blocked, id=55196, stack(0x000000e74fe00000,0x000000e74ff00000)]
  0x000002abdb53dbf0 JavaThread "Daemon worker" [_thread_blocked, id=82392, stack(0x000000e74ff00000,0x000000e750000000)]
  0x000002abdb671f00 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:60529 to /127.0.0.1:60531" [_thread_blocked, id=78520, stack(0x000000e750000000,0x000000e750100000)]
  0x000002abdb6723d0 JavaThread "Stdin handler" [_thread_blocked, id=80044, stack(0x000000e750100000,0x000000e750200000)]
  0x000002abdb671560 JavaThread "Daemon client event forwarder" [_thread_blocked, id=65840, stack(0x000000e750200000,0x000000e750300000)]
  0x000002abdb673710 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)" [_thread_in_Java, id=81136, stack(0x000000e750300000,0x000000e750400000)]
  0x000002abdb671a30 JavaThread "File lock request listener" [_thread_in_native, id=66812, stack(0x000000e750400000,0x000000e750500000)]
  0x000002abdb6740b0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.12\fileHashes)" [_thread_blocked, id=26092, stack(0x000000e750600000,0x000000e750700000)]
  0x000002abdb673be0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\Documents\augment-projects\srsrmain\frontend\android\.gradle\8.12\fileHashes)" [_thread_blocked, id=80124, stack(0x000000e750700000,0x000000e750800000)]
  0x000002abdb674580 JavaThread "Cache worker for Build Output Cleanup Cache (C:\Users\<USER>\Documents\augment-projects\srsrmain\frontend\android\.gradle\buildOutputCleanup)" [_thread_blocked, id=66416, stack(0x000000e750800000,0x000000e750900000)]
  0x000002abdb674a50 JavaThread "File watcher server" daemon [_thread_in_native, id=79944, stack(0x000000e750c00000,0x000000e750d00000)]
  0x000002abdb6728a0 JavaThread "File watcher consumer" daemon [_thread_blocked, id=68564, stack(0x000000e750d00000,0x000000e750e00000)]
  0x000002abdb673240 JavaThread "jar transforms" [_thread_blocked, id=81532, stack(0x000000e750e00000,0x000000e750f00000)]
  0x000002abdb671090 JavaThread "jar transforms Thread 2" [_thread_blocked, id=81056, stack(0x000000e750f00000,0x000000e751000000)]
  0x000002abdd5f5be0 JavaThread "jar transforms Thread 3" [_thread_blocked, id=58476, stack(0x000000e751000000,0x000000e751100000)]
  0x000002abdd5f73f0 JavaThread "jar transforms Thread 4" [_thread_blocked, id=59188, stack(0x000000e750500000,0x000000e750600000)]
  0x000002abdd5f4d70 JavaThread "jar transforms Thread 5" [_thread_blocked, id=80752, stack(0x000000e751300000,0x000000e751400000)]
  0x000002abdd5f6580 JavaThread "Cache worker for checksums cache (C:\Users\<USER>\Documents\augment-projects\srsrmain\frontend\android\.gradle\8.12\checksums)" [_thread_blocked, id=64992, stack(0x000000e751400000,0x000000e751500000)]
  0x000002abdd5f43d0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.12\fileContent)" [_thread_blocked, id=74424, stack(0x000000e751500000,0x000000e751600000)]
  0x000002abdd5f60b0 JavaThread "jar transforms Thread 6" [_thread_blocked, id=11632, stack(0x000000e751600000,0x000000e751700000)]
  0x000002abdd5f7d90 JavaThread "jar transforms Thread 7" [_thread_blocked, id=78980, stack(0x000000e751700000,0x000000e751800000)]
  0x000002abdd5f6f20 JavaThread "jar transforms Thread 8" [_thread_blocked, id=77852, stack(0x000000e751800000,0x000000e751900000)]
  0x000002abdd5f8260 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.12\md-rule)" [_thread_blocked, id=19432, stack(0x000000e751900000,0x000000e751a00000)]
  0x000002abdd5fb280 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.12\md-supplier)" [_thread_blocked, id=56076, stack(0x000000e751a00000,0x000000e751b00000)]
  0x000002abdd5f8730 JavaThread "Cache worker for Build Output Cleanup Cache (C:\src\flutter\packages\flutter_tools\gradle\.gradle\buildOutputCleanup)" [_thread_blocked, id=51352, stack(0x000000e751b00000,0x000000e751c00000)]
  0x000002abddaa6840 JavaThread "C2 CompilerThread1" daemon [_thread_in_native, id=78372, stack(0x000000e751c00000,0x000000e751d00000)]
  0x000002abdd5f8c00 JavaThread "Unconstrained build operations" [_thread_blocked, id=64948, stack(0x000000e751d00000,0x000000e751e00000)]
  0x000002abdd5f90d0 JavaThread "Unconstrained build operations Thread 2" [_thread_blocked, id=20484, stack(0x000000e751e00000,0x000000e751f00000)]
  0x000002abdd5f95a0 JavaThread "Unconstrained build operations Thread 3" [_thread_blocked, id=73444, stack(0x000000e751f00000,0x000000e752000000)]
  0x000002abdd5f9a70 JavaThread "Unconstrained build operations Thread 4" [_thread_blocked, id=43400, stack(0x000000e752000000,0x000000e752100000)]
  0x000002abdd5fa8e0 JavaThread "Unconstrained build operations Thread 5" [_thread_blocked, id=74508, stack(0x000000e752100000,0x000000e752200000)]
  0x000002abdd6b1780 JavaThread "Unconstrained build operations Thread 6" [_thread_blocked, id=82812, stack(0x000000e752200000,0x000000e752300000)]
  0x000002abdd6b0de0 JavaThread "Unconstrained build operations Thread 7" [_thread_blocked, id=32064, stack(0x000000e752300000,0x000000e752400000)]
  0x000002abdd6b12b0 JavaThread "Unconstrained build operations Thread 8" [_thread_blocked, id=28632, stack(0x000000e752400000,0x000000e752500000)]
  0x000002abdd6aff70 JavaThread "Unconstrained build operations Thread 9" [_thread_blocked, id=81960, stack(0x000000e752500000,0x000000e752600000)]
  0x000002abdd6b2ac0 JavaThread "Unconstrained build operations Thread 10" [_thread_blocked, id=74980, stack(0x000000e752600000,0x000000e752700000)]
  0x000002abdd6af5d0 JavaThread "Unconstrained build operations Thread 11" [_thread_blocked, id=48536, stack(0x000000e752700000,0x000000e752800000)]
  0x000002abdd6b25f0 JavaThread "Unconstrained build operations Thread 12" [_thread_blocked, id=72652, stack(0x000000e752800000,0x000000e752900000)]
  0x000002abdd6b1c50 JavaThread "Unconstrained build operations Thread 13" [_thread_blocked, id=74088, stack(0x000000e752900000,0x000000e752a00000)]
  0x000002abdd6af100 JavaThread "Unconstrained build operations Thread 14" [_thread_blocked, id=77916, stack(0x000000e752a00000,0x000000e752b00000)]
  0x000002abdd6afaa0 JavaThread "Unconstrained build operations Thread 15" [_thread_blocked, id=74136, stack(0x000000e752b00000,0x000000e752c00000)]
  0x000002abdd6b2120 JavaThread "Unconstrained build operations Thread 16" [_thread_blocked, id=70256, stack(0x000000e752c00000,0x000000e752d00000)]
  0x000002abdd6b3460 JavaThread "Unconstrained build operations Thread 17" [_thread_blocked, id=74628, stack(0x000000e752d00000,0x000000e752e00000)]
  0x000002abdd6b2f90 JavaThread "Unconstrained build operations Thread 18" [_thread_blocked, id=49028, stack(0x000000e752e00000,0x000000e752f00000)]
  0x000002abdd6b0440 JavaThread "Unconstrained build operations Thread 19" [_thread_blocked, id=67932, stack(0x000000e752f00000,0x000000e753000000)]
  0x000002abdd6b3930 JavaThread "Unconstrained build operations Thread 20" [_thread_blocked, id=18236, stack(0x000000e753000000,0x000000e753100000)]
  0x000002abdd6b3e00 JavaThread "Unconstrained build operations Thread 21" [_thread_blocked, id=5448, stack(0x000000e753100000,0x000000e753200000)]

Other Threads:
  0x000002abd5fbb6d0 VMThread "VM Thread" [stack: 0x000000e74e700000,0x000000e74e800000] [id=82484]
  0x000002abd5e0a090 WatcherThread [stack: 0x000000e74f300000,0x000000e74f400000] [id=61824]
  0x000002ab9b13cfb0 GCTaskThread "GC Thread#0" [stack: 0x000000e74e200000,0x000000e74e300000] [id=72956]
  0x000002abd98fe1e0 GCTaskThread "GC Thread#1" [stack: 0x000000e74f600000,0x000000e74f700000] [id=66412]
  0x000002abd9972f10 GCTaskThread "GC Thread#2" [stack: 0x000000e74f700000,0x000000e74f800000] [id=20608]
  0x000002abd99731c0 GCTaskThread "GC Thread#3" [stack: 0x000000e74f800000,0x000000e74f900000] [id=32952]
  0x000002abd9ab1030 GCTaskThread "GC Thread#4" [stack: 0x000000e74f900000,0x000000e74fa00000] [id=80616]
  0x000002abd9ab12e0 GCTaskThread "GC Thread#5" [stack: 0x000000e74fa00000,0x000000e74fb00000] [id=72212]
  0x000002abdbf68df0 GCTaskThread "GC Thread#6" [stack: 0x000000e750900000,0x000000e750a00000] [id=82900]
  0x000002abdbf69350 GCTaskThread "GC Thread#7" [stack: 0x000000e750a00000,0x000000e750b00000] [id=68252]
  0x000002ab9b18c010 ConcurrentGCThread "G1 Main Marker" [stack: 0x000000e74e300000,0x000000e74e400000] [id=82696]
  0x000002ab9b18df80 ConcurrentGCThread "G1 Conc#0" [stack: 0x000000e74e400000,0x000000e74e500000] [id=76044]
  0x000002abdbf67870 ConcurrentGCThread "G1 Conc#1" [stack: 0x000000e750b00000,0x000000e750c00000] [id=48904]
  0x000002abd5e71c70 ConcurrentGCThread "G1 Refine#0" [stack: 0x000000e74e500000,0x000000e74e600000] [id=67280]
  0x000002abdd9c8cf0 ConcurrentGCThread "G1 Refine#1" [stack: 0x000000e751100000,0x000000e751200000] [id=59044]
  0x000002abdd9c92b0 ConcurrentGCThread "G1 Refine#2" [stack: 0x000000e751200000,0x000000e751300000] [id=38400]
  0x000002abd5e72440 ConcurrentGCThread "G1 Service" [stack: 0x000000e74e600000,0x000000e74e700000] [id=82784]

Threads with active compile tasks:
C2 CompilerThread0    20673 6275       4       java.io.ObjectOutputStream::writeClassDesc (60 bytes)
C1 CompilerThread0    20673 6688       3       org.gradle.internal.snapshot.impl.DefaultValueSnapshotter$ValueSnapshotVisitor::hashCode (6 bytes)
C2 CompilerThread1    20673 6535   !   4       java.io.ObjectOutputStream::writeObject0 (620 bytes)

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x000002ab98ea72b0] Threads_lock - owner thread: 0x000002abd5fbb6d0
[0x000002ab9b0c15f0] Heap_lock - owner thread: 0x000002abdb671560

Heap address: 0x0000000600000000, size: 8192 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000800000000-0x0000000800bc0000-0x0000000800bc0000), size 12320768, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 0.
Compressed class space mapped at: 0x0000000800c00000-0x0000000840c00000, reserved size: 1073741824
Narrow klass base: 0x0000000800000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CPUs: 8 total, 8 available
 Memory: 16126M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 4M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 252M
 Heap Max Capacity: 8G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 110592K, used 82034K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 12 young (49152K), 2 survivors (8192K)
 Metaspace       used 50403K, committed 51008K, reserved 1097728K
  class space    used 7051K, committed 7296K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, OA=open archive, CA=closed archive, TAMS=top-at-mark-start (previous, next)
|   0|0x0000000600000000, 0x0000000600400000, 0x0000000600400000|100%|HS|  |TAMS 0x0000000600400000, 0x0000000600000000| Complete 
|   1|0x0000000600400000, 0x0000000600800000, 0x0000000600800000|100%| O|  |TAMS 0x0000000600800000, 0x0000000600400000| Untracked 
|   2|0x0000000600800000, 0x0000000600c00000, 0x0000000600c00000|100%| O|  |TAMS 0x0000000600800000, 0x0000000600800000| Untracked 
|   3|0x0000000600c00000, 0x0000000601000000, 0x0000000601000000|100%| O|  |TAMS 0x0000000601000000, 0x0000000600c00000| Untracked 
|   4|0x0000000601000000, 0x0000000601400000, 0x0000000601400000|100%| O|  |TAMS 0x0000000601400000, 0x0000000601000000| Untracked 
|   5|0x0000000601400000, 0x0000000601800000, 0x0000000601800000|100%| O|  |TAMS 0x0000000601800000, 0x0000000601400000| Untracked 
|   6|0x0000000601800000, 0x0000000601c00000, 0x0000000601c00000|100%| O|  |TAMS 0x0000000601c00000, 0x0000000601800000| Untracked 
|   7|0x0000000601c00000, 0x0000000601c14178, 0x0000000602000000|  1%| O|  |TAMS 0x0000000601c00000, 0x0000000601c00000| Untracked 
|   8|0x0000000602000000, 0x0000000602400000, 0x0000000602400000|100%| O|  |TAMS 0x0000000602000000, 0x0000000602000000| Untracked 
|   9|0x0000000602400000, 0x0000000602800000, 0x0000000602800000|100%| O|  |TAMS 0x0000000602400000, 0x0000000602400000| Untracked 
|  10|0x0000000602800000, 0x0000000602800000, 0x0000000602c00000|  0%| F|  |TAMS 0x0000000602800000, 0x0000000602800000| Untracked 
|  11|0x0000000602c00000, 0x0000000602c00000, 0x0000000603000000|  0%| F|  |TAMS 0x0000000602c00000, 0x0000000602c00000| Untracked 
|  12|0x0000000603000000, 0x0000000603000000, 0x0000000603400000|  0%| F|  |TAMS 0x0000000603000000, 0x0000000603000000| Untracked 
|  13|0x0000000603400000, 0x000000060362fd78, 0x0000000603800000| 57%| S|  |TAMS 0x0000000603400000, 0x0000000603400000| Complete 
|  14|0x0000000603800000, 0x0000000603994e80, 0x0000000603c00000| 39%| E|CS|TAMS 0x0000000603800000, 0x0000000603800000| Complete 
|  15|0x0000000603c00000, 0x0000000604000000, 0x0000000604000000|100%| S|  |TAMS 0x0000000603c00000, 0x0000000603c00000| Complete 
|  16|0x0000000604000000, 0x0000000604400000, 0x0000000604400000|100%| E|CS|TAMS 0x0000000604000000, 0x0000000604000000| Complete 
|  17|0x0000000604400000, 0x0000000604800000, 0x0000000604800000|100%| E|CS|TAMS 0x0000000604400000, 0x0000000604400000| Complete 
|  18|0x0000000604800000, 0x0000000604c00000, 0x0000000604c00000|100%| E|CS|TAMS 0x0000000604800000, 0x0000000604800000| Complete 
|  19|0x0000000604c00000, 0x0000000605000000, 0x0000000605000000|100%| E|CS|TAMS 0x0000000604c00000, 0x0000000604c00000| Complete 
|  20|0x0000000605000000, 0x0000000605400000, 0x0000000605400000|100%| E|CS|TAMS 0x0000000605000000, 0x0000000605000000| Complete 
|  21|0x0000000605400000, 0x0000000605800000, 0x0000000605800000|100%| E|CS|TAMS 0x0000000605400000, 0x0000000605400000| Complete 
|  28|0x0000000607000000, 0x0000000607400000, 0x0000000607400000|100%| E|CS|TAMS 0x0000000607000000, 0x0000000607000000| Complete 
|  29|0x0000000607400000, 0x0000000607800000, 0x0000000607800000|100%| E|CS|TAMS 0x0000000607400000, 0x0000000607400000| Complete 
|  30|0x0000000607800000, 0x0000000607c00000, 0x0000000607c00000|100%| E|CS|TAMS 0x0000000607800000, 0x0000000607800000| Complete 
|  31|0x0000000607c00000, 0x0000000608000000, 0x0000000608000000|100%| E|CS|TAMS 0x0000000607c00000, 0x0000000607c00000| Complete 
|  62|0x000000060f800000, 0x000000060fc00000, 0x000000060fc00000|100%| E|CS|TAMS 0x000000060f800000, 0x000000060f800000| Complete 

Card table byte_map: [0x000002abc3e00000,0x000002abc4e00000] _byte_map_base: 0x000002abc0e00000

Marking Bits (Prev, Next): (CMBitMap*) 0x000002ab9b13d4d0, (CMBitMap*) 0x000002ab9b13d510
 Prev Bits: [0x000002abc5e00000, 0x000002abcde00000)
 Next Bits: [0x000002abcde00000, 0x000002abd5e00000)

Polling page: 0x000002ab98ff0000

Metaspace:

Usage:
  Non-class:     42.34 MB used.
      Class:      6.89 MB used.
       Both:     49.22 MB used.

Virtual space:
  Non-class space:       48.00 MB reserved,      42.69 MB ( 89%) committed,  6 nodes.
      Class space:        1.00 GB reserved,       7.12 MB ( <1%) committed,  1 nodes.
             Both:        1.05 GB reserved,      49.81 MB (  5%) committed. 

Chunk freelists:
   Non-Class:  752.00 KB
       Class:  773.00 KB
        Both:  1.49 MB

MaxMetaspaceSize: 4.00 GB
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 58.75 MB
CDS: on
MetaspaceReclaimPolicy: balanced
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 1048576.
 - enlarge_chunks_in_place: 1.
 - new_chunks_are_fully_committed: 0.
 - uncommit_free_chunks: 1.
 - use_allocation_guard: 0.
 - handle_deallocations: 1.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 746.
num_arena_deaths: 0.
num_vsnodes_births: 7.
num_vsnodes_deaths: 0.
num_space_committed: 797.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 2788.
num_chunk_merges: 6.
num_chunk_splits: 1798.
num_chunks_enlarged: 1189.
num_purges: 0.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=259264Kb used=3302Kb max_used=3302Kb free=255961Kb
 bounds [0x000002abb2b80000, 0x000002abb2ec0000, 0x000002abc28b0000]
CodeHeap 'profiled nmethods': size=259264Kb used=11541Kb max_used=11541Kb free=247722Kb
 bounds [0x000002aba2e50000, 0x000002aba39a0000, 0x000002abb2b80000]
CodeHeap 'non-nmethods': size=5760Kb used=2347Kb max_used=2402Kb free=3412Kb
 bounds [0x000002aba28b0000, 0x000002aba2b20000, 0x000002aba2e50000]
 total_blobs=6679 nmethods=5828 adapters=762
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 19.599 Thread 0x000002abd9420090 nmethod 6590 0x000002aba3960690 code [0x000002aba3960840, 0x000002aba3960a68]
Event: 19.599 Thread 0x000002abd9420090 6591       3       com.google.common.collect.ImmutableListMultimap::builder (8 bytes)
Event: 19.600 Thread 0x000002abd9420090 nmethod 6591 0x000002aba3960b10 code [0x000002aba3960d00, 0x000002aba3961278]
Event: 19.600 Thread 0x000002abd9420090 6593       3       com.google.common.collect.ImmutableListMultimap::fromMapEntries (141 bytes)
Event: 19.602 Thread 0x000002abd9420090 nmethod 6593 0x000002aba3961490 code [0x000002aba3961880, 0x000002aba39638c8]
Event: 19.602 Thread 0x000002abd9420090 6595       3       com.google.common.collect.CollectCollectors$$Lambda$224/0x0000000800e51730::accept (9 bytes)
Event: 19.602 Thread 0x000002abd9420090 nmethod 6595 0x000002aba3964290 code [0x000002aba3964440, 0x000002aba3964688]
Event: 19.602 Thread 0x000002abd9420090 6592       3       com.google.common.collect.ImmutableMultimap$Builder::<init> (12 bytes)
Event: 19.602 Thread 0x000002abd9420090 nmethod 6592 0x000002aba3964790 code [0x000002aba3964960, 0x000002aba3964dd8]
Event: 19.602 Thread 0x000002abd9420090 6594       1       sun.nio.fs.WindowsPath::getPathForPermissionCheck (5 bytes)
Event: 19.602 Thread 0x000002abd9420090 nmethod 6594 0x000002abb2eb7910 code [0x000002abb2eb7aa0, 0x000002abb2eb7b78]
Event: 19.607 Thread 0x000002abd9420090 6596       3       java.util.stream.ForEachOps$ForEachOp::getOpFlags (15 bytes)
Event: 19.607 Thread 0x000002abd9420090 nmethod 6596 0x000002aba3964f90 code [0x000002aba3965120, 0x000002aba39652b8]
Event: 19.607 Thread 0x000002abd9420090 6597       3       java.util.stream.ForEachOps$ForEachOp$OfRef::evaluateSequential (7 bytes)
Event: 19.608 Thread 0x000002abd9420090 nmethod 6597 0x000002aba3965390 code [0x000002aba3965560, 0x000002aba3965988]
Event: 19.608 Thread 0x000002abd9420090 6598       3       java.util.stream.ForEachOps$ForEachOp::evaluateSequential (13 bytes)
Event: 19.608 Thread 0x000002abd9420090 nmethod 6598 0x000002aba3965b10 code [0x000002aba3965cc0, 0x000002aba3966088]
Event: 19.608 Thread 0x000002abd9420090 6600       3       java.util.LinkedHashMap::afterNodeRemoval (65 bytes)
Event: 19.609 Thread 0x000002abd9420090 nmethod 6600 0x000002aba3966190 code [0x000002aba3966360, 0x000002aba3966858]
Event: 19.609 Thread 0x000002abd9420090 6601   !   3       java.nio.file.Files::walkFileTree (297 bytes)

GC Heap History (20 events):
Event: 6.790 GC heap before
{Heap before GC invocations=3 (full 0):
 garbage-first heap   total 258048K, used 131270K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 31 young (126976K), 2 survivors (8192K)
 Metaspace       used 21127K, committed 21504K, reserved 1073152K
  class space    used 3089K, committed 3264K, reserved 1048576K
}
Event: 6.802 GC heap after
{Heap after GC invocations=4 (full 0):
 garbage-first heap   total 258048K, used 23810K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 4 young (16384K), 4 survivors (16384K)
 Metaspace       used 21127K, committed 21504K, reserved 1073152K
  class space    used 3089K, committed 3264K, reserved 1048576K
}
Event: 9.966 GC heap before
{Heap before GC invocations=5 (full 0):
 garbage-first heap   total 110592K, used 72962K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 16 young (65536K), 4 survivors (16384K)
 Metaspace       used 27182K, committed 27584K, reserved 1073152K
  class space    used 4003K, committed 4224K, reserved 1048576K
}
Event: 9.980 GC heap after
{Heap after GC invocations=6 (full 0):
 garbage-first heap   total 110592K, used 27365K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 27182K, committed 27584K, reserved 1073152K
  class space    used 4003K, committed 4224K, reserved 1048576K
}
Event: 10.560 GC heap before
{Heap before GC invocations=6 (full 0):
 garbage-first heap   total 110592K, used 31461K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 1 survivors (4096K)
 Metaspace       used 27723K, committed 28224K, reserved 1073152K
  class space    used 4089K, committed 4352K, reserved 1048576K
}
Event: 10.601 GC heap after
{Heap after GC invocations=7 (full 0):
 garbage-first heap   total 110592K, used 28625K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 27723K, committed 28224K, reserved 1073152K
  class space    used 4089K, committed 4352K, reserved 1048576K
}
Event: 12.961 GC heap before
{Heap before GC invocations=7 (full 0):
 garbage-first heap   total 110592K, used 77777K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 13 young (53248K), 1 survivors (4096K)
 Metaspace       used 33489K, committed 33984K, reserved 1081344K
  class space    used 4699K, committed 4928K, reserved 1048576K
}
Event: 12.969 GC heap after
{Heap after GC invocations=8 (full 0):
 garbage-first heap   total 110592K, used 32906K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 33489K, committed 33984K, reserved 1081344K
  class space    used 4699K, committed 4928K, reserved 1048576K
}
Event: 13.493 GC heap before
{Heap before GC invocations=8 (full 0):
 garbage-first heap   total 110592K, used 45194K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 7 young (28672K), 2 survivors (8192K)
 Metaspace       used 35504K, committed 35968K, reserved 1081344K
  class space    used 4875K, committed 5120K, reserved 1048576K
}
Event: 13.500 GC heap after
{Heap after GC invocations=9 (full 0):
 garbage-first heap   total 110592K, used 34567K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 35504K, committed 35968K, reserved 1081344K
  class space    used 4875K, committed 5120K, reserved 1048576K
}
Event: 14.336 GC heap before
{Heap before GC invocations=10 (full 0):
 garbage-first heap   total 110592K, used 79623K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 12 young (49152K), 1 survivors (4096K)
 Metaspace       used 38203K, committed 38720K, reserved 1089536K
  class space    used 5170K, committed 5440K, reserved 1048576K
}
Event: 14.343 GC heap after
{Heap after GC invocations=11 (full 0):
 garbage-first heap   total 110592K, used 36003K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 38203K, committed 38720K, reserved 1089536K
  class space    used 5170K, committed 5440K, reserved 1048576K
}
Event: 14.451 GC heap before
{Heap before GC invocations=11 (full 0):
 garbage-first heap   total 110592K, used 40099K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 1 survivors (4096K)
 Metaspace       used 38638K, committed 39168K, reserved 1089536K
  class space    used 5243K, committed 5504K, reserved 1048576K
}
Event: 14.462 GC heap after
{Heap after GC invocations=12 (full 0):
 garbage-first heap   total 110592K, used 35886K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 38638K, committed 39168K, reserved 1089536K
  class space    used 5243K, committed 5504K, reserved 1048576K
}
Event: 15.875 GC heap before
{Heap before GC invocations=12 (full 0):
 garbage-first heap   total 110592K, used 80942K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 12 young (49152K), 1 survivors (4096K)
 Metaspace       used 42983K, committed 43584K, reserved 1089536K
  class space    used 5930K, committed 6208K, reserved 1048576K
}
Event: 15.881 GC heap after
{Heap after GC invocations=13 (full 0):
 garbage-first heap   total 110592K, used 37299K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 1 young (4096K), 1 survivors (4096K)
 Metaspace       used 42983K, committed 43584K, reserved 1089536K
  class space    used 5930K, committed 6208K, reserved 1048576K
}
Event: 17.077 GC heap before
{Heap before GC invocations=13 (full 0):
 garbage-first heap   total 110592K, used 82355K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 12 young (49152K), 1 survivors (4096K)
 Metaspace       used 45129K, committed 45696K, reserved 1089536K
  class space    used 6260K, committed 6528K, reserved 1048576K
}
Event: 17.083 GC heap after
{Heap after GC invocations=14 (full 0):
 garbage-first heap   total 110592K, used 39354K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 45129K, committed 45696K, reserved 1089536K
  class space    used 6260K, committed 6528K, reserved 1048576K
}
Event: 18.312 GC heap before
{Heap before GC invocations=14 (full 0):
 garbage-first heap   total 110592K, used 80314K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 12 young (49152K), 2 survivors (8192K)
 Metaspace       used 48744K, committed 49408K, reserved 1097728K
  class space    used 6837K, committed 7104K, reserved 1048576K
}
Event: 18.319 GC heap after
{Heap after GC invocations=15 (full 0):
 garbage-first heap   total 110592K, used 41074K [0x0000000600000000, 0x0000000800000000)
  region size 4096K, 2 young (8192K), 2 survivors (8192K)
 Metaspace       used 48744K, committed 49408K, reserved 1097728K
  class space    used 6837K, committed 7104K, reserved 1048576K
}

Deoptimization events (20 events):
Event: 19.072 Thread 0x000002abdd6aff70 DEOPT PACKING pc=0x000002abb2d2e4c0 sp=0x000000e7525fd280
Event: 19.072 Thread 0x000002abdd6aff70 DEOPT UNPACKING pc=0x000002aba2905c23 sp=0x000000e7525fd228 mode 2
Event: 19.151 Thread 0x000002abdb53dbf0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002abb2cd76b4 relative=0x00000000000005b4
Event: 19.151 Thread 0x000002abdb53dbf0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002abb2cd76b4 method=org.gradle.internal.snapshot.VfsRelativePath.isPrefix(Ljava/lang/String;ILjava/lang/String;ILorg/gradle/internal/snapshot/CaseSensitivity;)Z @ 101 c2
Event: 19.152 Thread 0x000002abdb53dbf0 DEOPT PACKING pc=0x000002abb2cd76b4 sp=0x000000e74fff0d80
Event: 19.153 Thread 0x000002abdb53dbf0 DEOPT UNPACKING pc=0x000002aba2905c23 sp=0x000000e74fff0d08 mode 2
Event: 19.265 Thread 0x000002abdd6b3e00 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002abb2cbff98 relative=0x0000000000000278
Event: 19.265 Thread 0x000002abdd6b3e00 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002abb2cbff98 method=java.util.concurrent.ConcurrentHashMap.get(Ljava/lang/Object;)Ljava/lang/Object; @ 86 c2
Event: 19.265 Thread 0x000002abdd6b3e00 DEOPT PACKING pc=0x000002abb2cbff98 sp=0x000000e7531fdf30
Event: 19.265 Thread 0x000002abdd6b3e00 DEOPT UNPACKING pc=0x000002aba2905c23 sp=0x000000e7531fdea8 mode 2
Event: 19.293 Thread 0x000002abdd6b3460 DEOPT PACKING pc=0x000002aba3272574 sp=0x000000e752dfc8d0
Event: 19.293 Thread 0x000002abdd6b3460 DEOPT UNPACKING pc=0x000002aba2906763 sp=0x000000e752dfbe50 mode 0
Event: 19.435 Thread 0x000002abdb53dbf0 DEOPT PACKING pc=0x000002aba3272574 sp=0x000000e74fff3120
Event: 19.435 Thread 0x000002abdb53dbf0 DEOPT UNPACKING pc=0x000002aba2906763 sp=0x000000e74fff26a0 mode 0
Event: 19.493 Thread 0x000002abdb6740b0 DEOPT PACKING pc=0x000002aba3818371 sp=0x000000e7506fe7f0
Event: 19.493 Thread 0x000002abdb6740b0 DEOPT UNPACKING pc=0x000002aba2906763 sp=0x000000e7506fdc90 mode 0
Event: 19.550 Thread 0x000002abdd6b3e00 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002abb2d6cb18 relative=0x0000000000000458
Event: 19.550 Thread 0x000002abdd6b3e00 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002abb2d6cb18 method=java.util.concurrent.ConcurrentHashMap.addCount(JI)V @ 97 c2
Event: 19.550 Thread 0x000002abdd6b3e00 DEOPT PACKING pc=0x000002abb2d6cb18 sp=0x000000e7531fd150
Event: 19.550 Thread 0x000002abdd6b3e00 DEOPT UNPACKING pc=0x000002aba2905c23 sp=0x000000e7531fd028 mode 2

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 16.812 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006044b4a48}: org/gradle/configuration/internal/DefaultListenerBuildOperationDecorator$BuildOperationEmittingClosureBeanInfo> (0x00000006044b4a48) 
thrown [t:\workspace\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 16.813 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/ClassNotFoundException'{0x00000006044d2588}: org/gradle/configuration/internal/DefaultListenerBuildOperationDecorator$BuildOperationEmittingClosureCustomizer> (0x00000006044d2588) 
thrown [t:\workspace\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 16.832 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/ClassNotFoundException'{0x0000000604553028}: org/gradle/api/internal/plugins/DefaultObjectConfigurationActionBeanInfo> (0x0000000604553028) 
thrown [t:\workspace\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 16.834 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/ClassNotFoundException'{0x0000000604570200}: org/gradle/api/internal/plugins/DefaultObjectConfigurationActionCustomizer> (0x0000000604570200) 
thrown [t:\workspace\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 16.967 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006041465e8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x00000006041465e8) 
thrown [t:\workspace\open\src\hotspot\share\interpreter\linkResolver.cpp, line 766]
Event: 17.243 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/ClassNotFoundException'{0x0000000607d108a0}: java/lang/StringBeanInfo> (0x0000000607d108a0) 
thrown [t:\workspace\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 17.244 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/ClassNotFoundException'{0x0000000607d1e340}: java/lang/StringCustomizer> (0x0000000607d1e340) 
thrown [t:\workspace\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 17.252 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/ClassNotFoundException'{0x0000000607d7e008}: java/io/FileBeanInfo> (0x0000000607d7e008) 
thrown [t:\workspace\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 17.253 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/ClassNotFoundException'{0x0000000607d8bb98}: java/io/FileCustomizer> (0x0000000607d8bb98) 
thrown [t:\workspace\open\src\hotspot\share\classfile\systemDictionary.cpp, line 256]
Event: 17.321 Thread 0x000002abdb671560 Exception <a 'java/lang/NoSuchMethodError'{0x0000000607f202c8}: static Lorg/gradle/internal/build/event/types/DefaultProjectConfigurationDescriptor;.<clinit>()V> (0x0000000607f202c8) 
thrown [t:\workspace\open\src\hotspot\share\prims\jni.cpp, line 1107]
Event: 17.872 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/NoSuchMethodError'{0x000000060569c560}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object)'> (0x000000060569c560) 
thrown [t:\workspace\open\src\hotspot\share\interpreter\linkResolver.cpp, line 766]
Event: 18.117 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000604f4dd40}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000604f4dd40) 
thrown [t:\workspace\open\src\hotspot\share\interpreter\linkResolver.cpp, line 766]
Event: 18.219 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/NoSuchMethodError'{0x000000060446ddd0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000060446ddd0) 
thrown [t:\workspace\open\src\hotspot\share\interpreter\linkResolver.cpp, line 766]
Event: 18.251 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/NoSuchMethodError'{0x000000060467e280}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000060467e280) 
thrown [t:\workspace\open\src\hotspot\share\interpreter\linkResolver.cpp, line 766]
Event: 18.347 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/NoSuchMethodError'{0x000000060f939160}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, long, java.lang.Object, java.lang.Object)'> (0x000000060f939160) 
thrown [t:\workspace\open\src\hotspot\share\interpreter\linkResolver.cpp, line 766]
Event: 18.858 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/IncompatibleClassChangeError'{0x0000000607fe2770}: Found class java.lang.Object, but interface was expected> (0x0000000607fe2770) 
thrown [t:\workspace\open\src\hotspot\share\interpreter\linkResolver.cpp, line 833]
Event: 18.909 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006078bf378}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x00000006078bf378) 
thrown [t:\workspace\open\src\hotspot\share\interpreter\linkResolver.cpp, line 766]
Event: 18.999 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/NoSuchMethodError'{0x000000060745cf30}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000060745cf30) 
thrown [t:\workspace\open\src\hotspot\share\interpreter\linkResolver.cpp, line 766]
Event: 19.000 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000607466528}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000607466528) 
thrown [t:\workspace\open\src\hotspot\share\interpreter\linkResolver.cpp, line 766]
Event: 19.248 Thread 0x000002abdb53dbf0 Exception <a 'java/lang/NoSuchMethodError'{0x00000006055f2ac8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecialIFC(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000006055f2ac8) 
thrown [t:\workspace\open\src\hotspot\share\interpreter\linkResolver.cpp, line 766]

VM Operations (20 events):
Event: 19.130 Executing VM operation: ICBufferFull done
Event: 19.172 Executing VM operation: ICBufferFull
Event: 19.173 Executing VM operation: ICBufferFull done
Event: 19.276 Executing VM operation: ICBufferFull
Event: 19.287 Executing VM operation: ICBufferFull done
Event: 19.317 Executing VM operation: ICBufferFull
Event: 19.325 Executing VM operation: ICBufferFull done
Event: 19.375 Executing VM operation: ICBufferFull
Event: 19.380 Executing VM operation: ICBufferFull done
Event: 19.416 Executing VM operation: ICBufferFull
Event: 19.417 Executing VM operation: ICBufferFull done
Event: 19.458 Executing VM operation: ICBufferFull
Event: 19.460 Executing VM operation: ICBufferFull done
Event: 19.506 Executing VM operation: ICBufferFull
Event: 19.510 Executing VM operation: ICBufferFull done
Event: 19.548 Executing VM operation: ICBufferFull
Event: 19.549 Executing VM operation: ICBufferFull done
Event: 19.586 Executing VM operation: ICBufferFull
Event: 19.592 Executing VM operation: ICBufferFull done
Event: 19.616 Executing VM operation: ICBufferFull

Events (20 events):
Event: 18.989 loading class sun/reflect/ReflectionFactory$1
Event: 18.989 loading class sun/reflect/ReflectionFactory$1 done
Event: 19.044 Thread 0x000002abdd6b12b0 Thread added: 0x000002abdd6b12b0
Event: 19.046 Thread 0x000002abdd6aff70 Thread added: 0x000002abdd6aff70
Event: 19.050 Thread 0x000002abdd6b2ac0 Thread added: 0x000002abdd6b2ac0
Event: 19.053 Thread 0x000002abdd6af5d0 Thread added: 0x000002abdd6af5d0
Event: 19.056 Thread 0x000002abdd6b25f0 Thread added: 0x000002abdd6b25f0
Event: 19.063 Thread 0x000002abdd6b1c50 Thread added: 0x000002abdd6b1c50
Event: 19.066 loading class java/nio/channels/Channels
Event: 19.066 loading class java/nio/channels/Channels done
Event: 19.066 loading class sun/nio/ch/ChannelInputStream
Event: 19.066 loading class sun/nio/ch/ChannelInputStream done
Event: 19.067 Thread 0x000002abdd6af100 Thread added: 0x000002abdd6af100
Event: 19.250 Thread 0x000002abdd6afaa0 Thread added: 0x000002abdd6afaa0
Event: 19.251 Thread 0x000002abdd6b2120 Thread added: 0x000002abdd6b2120
Event: 19.252 Thread 0x000002abdd6b3460 Thread added: 0x000002abdd6b3460
Event: 19.252 Thread 0x000002abdd6b2f90 Thread added: 0x000002abdd6b2f90
Event: 19.256 Thread 0x000002abdd6b0440 Thread added: 0x000002abdd6b0440
Event: 19.259 Thread 0x000002abdd6b3930 Thread added: 0x000002abdd6b3930
Event: 19.260 Thread 0x000002abdd6b3e00 Thread added: 0x000002abdd6b3e00


Dynamic libraries:
0x00007ff688710000 - 0x00007ff68871e000 	D:\Softwares\jdk-17\bin\java.exe
0x00007ffd10bc0000 - 0x00007ffd10e26000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffd0f990000 - 0x00007ffd0fa59000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffd0e470000 - 0x00007ffd0e83c000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffd0df00000 - 0x00007ffd0e04b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffd02e60000 - 0x00007ffd02e78000 	D:\Softwares\jdk-17\bin\jli.dll
0x00007ffd03d20000 - 0x00007ffd03d3a000 	D:\Softwares\jdk-17\bin\VCRUNTIME140.dll
0x00007ffd0ede0000 - 0x00007ffd0efaa000 	C:\WINDOWS\System32\USER32.dll
0x00007ffd0ded0000 - 0x00007ffd0def7000 	C:\WINDOWS\System32\win32u.dll
0x00007ffd0ebe0000 - 0x00007ffd0ec0b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffcf46c0000 - 0x00007ffcf495a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e\COMCTL32.dll
0x00007ffd0e840000 - 0x00007ffd0e972000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffd10970000 - 0x00007ffd10a19000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffd0e260000 - 0x00007ffd0e303000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffd0ec10000 - 0x00007ffd0ec40000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffd02e00000 - 0x00007ffd02e0c000 	D:\Softwares\jdk-17\bin\vcruntime140_1.dll
0x00007ffccf190000 - 0x00007ffccf21d000 	D:\Softwares\jdk-17\bin\msvcp140.dll
0x00007ffc8ac80000 - 0x00007ffc8b83e000 	D:\Softwares\jdk-17\bin\server\jvm.dll
0x00007ffd10ac0000 - 0x00007ffd10b72000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffd0ed00000 - 0x00007ffd0eda6000 	C:\WINDOWS\System32\sechost.dll
0x00007ffd0f7e0000 - 0x00007ffd0f8f6000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffd0f030000 - 0x00007ffd0f038000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffd03d70000 - 0x00007ffd03d7a000 	C:\WINDOWS\SYSTEM32\WSOCK32.dll
0x00007ffd00010000 - 0x00007ffd00046000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffd03700000 - 0x00007ffd0370b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffd10a20000 - 0x00007ffd10a94000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffd0cbf0000 - 0x00007ffd0cc0a000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffd02df0000 - 0x00007ffd02dfa000 	D:\Softwares\jdk-17\bin\jimage.dll
0x00007ffd0abc0000 - 0x00007ffd0ae01000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffd0fa70000 - 0x00007ffd0fdf4000 	C:\WINDOWS\System32\combase.dll
0x00007ffd0e980000 - 0x00007ffd0ea60000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffcedfc0000 - 0x00007ffcedff9000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffd0e3d0000 - 0x00007ffd0e469000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffd02cc0000 - 0x00007ffd02cce000 	D:\Softwares\jdk-17\bin\instrument.dll
0x00007ffce1f90000 - 0x00007ffce1fb5000 	D:\Softwares\jdk-17\bin\java.dll
0x00007ffccd670000 - 0x00007ffccd746000 	D:\Softwares\jdk-17\bin\svml.dll
0x00007ffd0f040000 - 0x00007ffd0f76d000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffd0e050000 - 0x00007ffd0e1c4000 	C:\WINDOWS\System32\wintypes.dll
0x00007ffd0b790000 - 0x00007ffd0bfe6000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffd0fe00000 - 0x00007ffd0feef000 	C:\WINDOWS\System32\SHCORE.dll
0x00007ffd0ea70000 - 0x00007ffd0ead9000 	C:\WINDOWS\System32\shlwapi.dll
0x00007ffd0dc60000 - 0x00007ffd0dc8f000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffd02ca0000 - 0x00007ffd02cb8000 	D:\Softwares\jdk-17\bin\zip.dll
0x00007ffcf41f0000 - 0x00007ffcf4209000 	D:\Softwares\jdk-17\bin\net.dll
0x00007ffd03520000 - 0x00007ffd0363e000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffd0d140000 - 0x00007ffd0d1aa000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffcee6a0000 - 0x00007ffcee6b5000 	D:\Softwares\jdk-17\bin\nio.dll
0x00007ffcf3730000 - 0x00007ffcf3740000 	D:\Softwares\jdk-17\bin\verify.dll
0x00007ffceb370000 - 0x00007ffceb397000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x000000006f980000 - 0x000000006f9f3000 	C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffcf36e0000 - 0x00007ffcf36e9000 	D:\Softwares\jdk-17\bin\management.dll
0x00007ffcf3640000 - 0x00007ffcf364b000 	D:\Softwares\jdk-17\bin\management_ext.dll
0x00007ffd0d500000 - 0x00007ffd0d51c000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffd0cb50000 - 0x00007ffd0cb8a000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffd0d1e0000 - 0x00007ffd0d20b000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffd0dc30000 - 0x00007ffd0dc56000 	C:\WINDOWS\SYSTEM32\bcrypt.dll
0x00007ffd0d370000 - 0x00007ffd0d37c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffd0c370000 - 0x00007ffd0c3a3000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffd10ab0000 - 0x00007ffd10aba000 	C:\WINDOWS\System32\NSI.dll
0x00007ffd047b0000 - 0x00007ffd047cf000 	C:\WINDOWS\SYSTEM32\dhcpcsvc6.DLL
0x00007ffd04720000 - 0x00007ffd04745000 	C:\WINDOWS\SYSTEM32\dhcpcsvc.DLL
0x00007ffd0c3b0000 - 0x00007ffd0c4d7000 	C:\WINDOWS\SYSTEM32\DNSAPI.dll
0x00007ffcf3620000 - 0x00007ffcf362e000 	D:\Softwares\jdk-17\bin\sunmscapi.dll
0x00007ffd0dd50000 - 0x00007ffd0dec7000 	C:\WINDOWS\System32\CRYPT32.dll
0x00007ffd0d650000 - 0x00007ffd0d680000 	C:\WINDOWS\SYSTEM32\ncrypt.dll
0x00007ffd0d600000 - 0x00007ffd0d63f000 	C:\WINDOWS\SYSTEM32\NTASN1.dll
0x00007ffcdd920000 - 0x00007ffcdd928000 	C:\WINDOWS\system32\wshunix.dll
0x000000006f900000 - 0x000000006f973000 	C:\Users\<USER>\AppData\Local\Temp\native-platform5127623185106588429dir\gradle-fileevents.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Softwares\jdk-17\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.3912_none_3e07963ce335137e;D:\Softwares\jdk-17\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.5\x86_64-windows-gnu;C:\Users\<USER>\AppData\Local\Temp\native-platform5127623185106588429dir

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -XX:MaxMetaspaceSize=4G -XX:ReservedCodeCacheSize=512m -XX:+HeapDumpOnOutOfMemoryError -Xmx8G -Dfile.encoding=UTF-8 -Duser.country=IN -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\agents\gradle-instrumentation-agent-8.12.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.12
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.12-all\ejduaidbjup3bmmkhw3rie4zb\gradle-8.12\lib\gradle-daemon-main-8.12.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
     bool HeapDumpOnOutOfMemoryError               = true                                   {manageable} {command line}
   size_t InitialHeapSize                          = 264241152                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8589934592                                {product} {command line}
   size_t MaxMetaspaceSize                         = 4294967296                                {product} {command line}
   size_t MaxNewSize                               = 5150605312                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 265515770                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 265515770                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 536870912                              {pd product} {command line}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8589934592                             {manageable} {ergonomic}
   double SweeperThreshold                         = 0.234375                                  {product} {ergonomic}
     bool UseCompressedClassPointers               = true                           {product lp64_product} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags
 #1: stderr all=off uptime,level,tags

Environment Variables:
JAVA_HOME=D:\Softwares\jdk-17
PATH=C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\Microsoft VS Code\bin;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Roaming\npm;C:\src\flutter\bin;C:\Users\<USER>\AppData\Local\pnpm;C:\Users\<USER>\.local\bin;D:\Softwares\Python312\Scripts\;D:\Softwares\Python312\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Git\cmd;C:\Users\<USER>\AppData\Roaming\npm;D:\Softwares\nodejsv23.6.1;D:\Softwares\jdk-17\bin;C:\Users\<USER>\AppData\Local\Programs\Ollama;D:\Softwares\apache-maven-3.9.9\bin;D:\Softwares\VSCode\bin;D:\Softwares\Temporal;C:\Users\<USER>\.lmstudio\bin;
USERNAME=NH0598
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 140 Stepping 1, GenuineIntel



---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 26100 (10.0.26100.3912)
OS uptime: 13 days 2:48 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 140 stepping 1 microcode 0xb8, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, avx512f, avx512dq, avx512cd, avx512bw, avx512vl, sha, fma, vzeroupper, avx512_vpopcntdq, avx512_vpclmulqdq, avx512_vaes, avx512_vnni, clflush, clflushopt, clwb, avx512_vbmi2, avx512_vbmi, hv

Memory: 4k page, system-wide physical 16126M (490M free)
TotalPageFile size 48241M (AvailPageFile size 8M)
current process WorkingSet (physical memory assigned to process): 283M, peak: 304M
current process commit charge ("private bytes"): 330M, peak: 431M

vm_info: OpenJDK 64-Bit Server VM (17+35-2724) for windows-amd64 JRE (17+35-2724), built on Aug  5 2021 23:26:02 by "mach5one" with MS VC++ 16.8 (VS2019)

END.
