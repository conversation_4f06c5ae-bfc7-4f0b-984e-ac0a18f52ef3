import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../../core/theme/app_theme.dart';
import '../../routes/app_router.dart';
import '../main/main_navigation_screen.dart';

class DashboardScreen extends ConsumerStatefulWidget {
  const DashboardScreen({super.key});

  @override
  ConsumerState<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends ConsumerState<DashboardScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Dashboard',
        actions: [
          IconButton(
            icon: Icon(Icons.notifications_outlined),
            onPressed: null, // TODO: Implement notifications
          ),
          IconButton(
            icon: Icon(Icons.refresh),
            onPressed: null, // TODO: Implement refresh
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // System Status Overview
              _buildSystemStatusOverview(),
              
              const SizedBox(height: 24),
              
              // Properties Quick View
              _buildPropertiesQuickView(),
              
              const SizedBox(height: 24),
              
              // System Health Summary
              _buildSystemHealthSummary(),
              
              const SizedBox(height: 24),
              
              // Critical Alerts Feed
              _buildCriticalAlerts(),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    // TODO: Implement refresh logic
    await Future.delayed(const Duration(seconds: 1));
  }

  Widget _buildSystemStatusOverview() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'System Status Overview',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.more_vert),
                  onPressed: () {
                    // TODO: Show more options
                  },
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Progress Bar
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Overall System Health',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    Text(
                      '80%',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.successColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: 0.8,
                  backgroundColor: Colors.grey[300],
                  valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.successColor),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // Quick Stats
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Total Functions',
                    '35',
                    Icons.functions,
                    AppTheme.infoColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Operational',
                    '28',
                    Icons.check_circle,
                    AppTheme.successColor,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Requires Action',
                    '4',
                    Icons.warning,
                    AppTheme.warningColor,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Critical',
                    '3',
                    Icons.error,
                    AppTheme.errorColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPropertiesQuickView() {
    final properties = [
      {'name': 'Jublee Hills Home', 'status': 'operational', 'systems': 6},
      {'name': 'Gandipet Guest House', 'status': 'warning', 'systems': 5},
      {'name': 'Brane - Back Office', 'status': 'operational', 'systems': 4},
      {'name': 'Construction Sites', 'status': 'critical', 'systems': 8},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Properties Quick View',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: () => context.go(AppRoutes.properties),
              child: const Text('View All'),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        ...properties.map((property) => _buildPropertyCard(property)),
      ],
    );
  }

  Widget _buildPropertyCard(Map<String, dynamic> property) {
    Color statusColor;
    switch (property['status']) {
      case 'operational':
        statusColor = AppTheme.successColor;
        break;
      case 'warning':
        statusColor = AppTheme.warningColor;
        break;
      case 'critical':
        statusColor = AppTheme.errorColor;
        break;
      default:
        statusColor = Colors.grey;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: statusColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            Icons.business,
            color: statusColor,
          ),
        ),
        title: Text(
          property['name'],
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text('${property['systems']} systems'),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            StatusIndicator(status: property['status'], showLabel: true),
            const SizedBox(width: 8),
            const Icon(Icons.chevron_right),
          ],
        ),
        onTap: () {
          // TODO: Navigate to property detail
          context.go('/properties/property-${property['name'].toLowerCase().replaceAll(' ', '-')}');
        },
      ),
    );
  }

  Widget _buildSystemHealthSummary() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'System Health Summary',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Pie Chart
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: [
                    PieChartSectionData(
                      value: 28,
                      title: 'Operational\n28',
                      color: AppTheme.successColor,
                      radius: 60,
                      titleStyle: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    PieChartSectionData(
                      value: 4,
                      title: 'Warning\n4',
                      color: AppTheme.warningColor,
                      radius: 60,
                      titleStyle: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    PieChartSectionData(
                      value: 3,
                      title: 'Critical\n3',
                      color: AppTheme.errorColor,
                      radius: 60,
                      titleStyle: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                  centerSpaceRadius: 40,
                  sectionsSpace: 2,
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // System Categories
            _buildSystemCategory('Electricity Status', 'operational', Icons.electrical_services),
            _buildSystemCategory('Water Systems', 'operational', Icons.water_drop),
            _buildSystemCategory('Security Status', 'warning', Icons.security),
            _buildSystemCategory('Internet Connectivity', 'operational', Icons.wifi),
            _buildSystemCategory('Maintenance Issues', 'critical', Icons.build),
            _buildSystemCategory('OTT Services', 'operational', Icons.tv),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemCategory(String title, String status, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          StatusIndicator(status: status, showLabel: true),
        ],
      ),
    );
  }

  Widget _buildCriticalAlerts() {
    final alerts = [
      {
        'title': 'Generator Fuel Low',
        'description': 'Jublee Hills Home - 20% fuel remaining',
        'time': '2 hours ago',
        'severity': 'critical',
      },
      {
        'title': 'CCTV Camera Offline',
        'description': 'Gandipet Guest House - Camera 3 not responding',
        'time': '4 hours ago',
        'severity': 'warning',
      },
      {
        'title': 'Water Tank Cleaning Due',
        'description': 'Scheduled maintenance overdue by 2 days',
        'time': '1 day ago',
        'severity': 'warning',
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Critical Alerts',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: Navigate to alerts screen
              },
              child: const Text('View All'),
            ),
          ],
        ),
        
        const SizedBox(height: 12),
        
        ...alerts.map((alert) => _buildAlertCard(alert)),
      ],
    );
  }

  Widget _buildAlertCard(Map<String, dynamic> alert) {
    Color severityColor = alert['severity'] == 'critical' 
        ? AppTheme.errorColor 
        : AppTheme.warningColor;

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: severityColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            alert['severity'] == 'critical' ? Icons.error : Icons.warning,
            color: severityColor,
          ),
        ),
        title: Text(
          alert['title'],
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(alert['description']),
            const SizedBox(height: 4),
            Text(
              alert['time'],
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        trailing: Icon(
          Icons.chevron_right,
          color: Colors.grey[400],
        ),
        onTap: () {
          // TODO: Navigate to alert detail
        },
      ),
    );
  }
}
